# Augment VSCode Extension Packaging Script
# Package vscode-augment-0.521.1-yuanban directory to .vsix file

# Set encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Set path variables
$SourceDir = "D:\augment-pojie\0.521\vscode-augment-0.521.1-yuanban"
$OutputDir = $SourceDir  # Output to source directory
$OutputName = "vscode-augment-0.521.1-repackaged.vsix"
$OutputPath = Join-Path -Path $OutputDir -ChildPath $OutputName

Write-Host "=== Augment VSCode Extension Packaging Tool ===" -ForegroundColor Cyan
Write-Host "Source Directory: $SourceDir" -ForegroundColor Yellow
Write-Host "Output Path: $OutputPath" -ForegroundColor Yellow

try {
    # Check source directory
    if (-not (Test-Path -Path $SourceDir)) {
        throw "Source directory does not exist: $SourceDir"
    }

    # Check required files
    $manifestPath = Join-Path -Path $SourceDir -ChildPath "extension.vsixmanifest"
    $contentTypesPath = Join-Path -Path $SourceDir -ChildPath "[Content_Types].xml"

    if (Test-Path -Path $manifestPath) {
        Write-Host "Found extension.vsixmanifest" -ForegroundColor Green
    } else {
        Write-Host "Warning: extension.vsixmanifest not found" -ForegroundColor Yellow
    }

    if (Test-Path -Path $contentTypesPath) {
        Write-Host "Found [Content_Types].xml" -ForegroundColor Green
    } else {
        Write-Host "Warning: [Content_Types].xml not found" -ForegroundColor Yellow
    }
    
    # Ensure output directory exists
    if (-not (Test-Path -Path $OutputDir)) {
        New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    }

    # Remove existing output file
    if (Test-Path -Path $OutputPath) {
        Write-Host "Removing existing file..." -ForegroundColor Yellow
        Remove-Item -Path $OutputPath -Force
    }

    Write-Host "Compressing files..." -ForegroundColor Green

    # Use .NET compression library
    Add-Type -AssemblyName System.IO.Compression.FileSystem

    # Create temporary directory outside source to avoid recursive packaging
    $tempDir = Join-Path -Path ([System.IO.Path]::GetTempPath()) -ChildPath "vsix-packaging-$(Get-Random)"
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

    try {
        # Copy all files except existing .vsix files to temp directory
        Write-Host "Copying files to temporary directory..." -ForegroundColor Yellow
        $excludePatterns = @("*.vsix", "*.tmp")

        Get-ChildItem -Path $SourceDir -Recurse | ForEach-Object {
            $relativePath = $_.FullName.Substring($SourceDir.Length + 1)
            $shouldExclude = $false

            foreach ($pattern in $excludePatterns) {
                if ($_.Name -like $pattern) {
                    $shouldExclude = $true
                    break
                }
            }

            if (-not $shouldExclude) {
                $destPath = Join-Path -Path $tempDir -ChildPath $relativePath
                $destDir = Split-Path -Path $destPath -Parent

                if (-not (Test-Path -Path $destDir)) {
                    New-Item -ItemType Directory -Path $destDir -Force | Out-Null
                }

                if ($_.PSIsContainer -eq $false) {
                    Copy-Item -Path $_.FullName -Destination $destPath -Force
                }
            }
        }

        # Create ZIP from temporary directory
        $tempZip = $OutputPath + ".tmp"
        [System.IO.Compression.ZipFile]::CreateFromDirectory($tempDir, $tempZip, [System.IO.Compression.CompressionLevel]::Optimal, $false)

        # Rename to final file
        Rename-Item -Path $tempZip -NewName $OutputPath

    } finally {
        # Clean up temporary directory
        if (Test-Path -Path $tempDir) {
            Remove-Item -Path $tempDir -Recurse -Force
        }
    }
    
    # Verify result
    if (Test-Path -Path $OutputPath) {
        $fileSize = (Get-Item -Path $OutputPath).Length
        Write-Host "Packaging successful!" -ForegroundColor Green
        Write-Host "Output file: $OutputPath" -ForegroundColor Cyan
        Write-Host "File size: $([math]::Round($fileSize/1MB, 2)) MB" -ForegroundColor Cyan

        # Show installation command
        Write-Host "`nInstallation command:" -ForegroundColor Yellow
        Write-Host "code --install-extension `"$OutputPath`"" -ForegroundColor White

        Write-Host "`nPackaging completed successfully!" -ForegroundColor Green
    } else {
        throw "File creation failed"
    }

} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
