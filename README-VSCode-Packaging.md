# VSCode插件打包工具使用说明

## 概述

本工具包含两个PowerShell脚本，用于将VSCode插件目录打包为标准的`.vsix`格式文件。

## 脚本文件

### 1. `package-augment-vsix.ps1` - 专用脚本
专门为当前项目设计的简化版本，直接打包指定的Augment插件目录。

**特点：**
- 预设路径，无需参数
- 简单快速执行
- 适合日常使用

**使用方法：**
```powershell
# 直接运行
.\package-augment-vsix.ps1

# 或者使用完整路径
powershell.exe -ExecutionPolicy Bypass -File "package-augment-vsix.ps1"
```

### 2. `package-vsix.ps1` - 通用脚本
功能完整的通用版本，支持自定义参数和详细的错误处理。

**特点：**
- 支持命令行参数
- 完整的错误处理机制
- 详细的进度显示
- 文件验证功能

**使用方法：**
```powershell
# 使用默认参数
.\package-vsix.ps1

# 自定义源目录
.\package-vsix.ps1 -SourcePath "C:\MyExtension" -OutputPath "C:\Output" -OutputName "my-extension"

# 查看帮助
Get-Help .\package-vsix.ps1 -Detailed
```

## 功能特性

### ✅ 核心功能
- 将指定目录压缩为ZIP格式
- 自动重命名为`.vsix`扩展名
- 验证VSCode插件必需文件
- 显示文件大小和安装命令

### ✅ 错误处理
- 源目录存在性检查
- 输出目录自动创建
- 已存在文件自动覆盖
- 详细的错误信息显示

### ✅ 文件验证
- 检查`extension.vsixmanifest`文件
- 检查`[Content_Types].xml`文件
- 显示压缩包内容统计

## 输出示例

```
=== Augment VSCode Extension Packaging Tool ===
Source Directory: D:\augment-pojie\0.521\vscode-augment-0.521.1-yuanban
Output Path: D:\augment-pojie\0.521\vscode-augment-0.521.1-repackaged.vsix
Found extension.vsixmanifest
Warning: [Content_Types].xml not found
Compressing files...
Packaging successful!
Output file: D:\augment-pojie\0.521\vscode-augment-0.521.1-repackaged.vsix
File size: 10.91 MB

Installation command:
code --install-extension "D:\augment-pojie\0.521\vscode-augment-0.521.1-repackaged.vsix"

Packaging completed successfully!
```

## 安装打包后的插件

生成的`.vsix`文件可以通过以下方式安装：

### 方法1：命令行安装
```bash
code --install-extension "path/to/your-extension.vsix"
```

### 方法2：VSCode界面安装
1. 打开VSCode
2. 按`Ctrl+Shift+P`打开命令面板
3. 输入"Extensions: Install from VSIX..."
4. 选择生成的`.vsix`文件

### 方法3：拖拽安装
直接将`.vsix`文件拖拽到VSCode窗口中

## 注意事项

1. **执行策略**：如果遇到执行策略限制，使用：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

2. **路径问题**：确保路径中不包含特殊字符，建议使用绝对路径

3. **文件权限**：确保对源目录有读取权限，对输出目录有写入权限

4. **磁盘空间**：确保输出目录有足够的磁盘空间

## 故障排除

### 常见问题

**Q: 提示"无法将参数绑定到参数"Path""**
A: 检查路径是否正确，确保使用`-Path`参数名

**Q: 压缩失败**
A: 检查源目录是否存在，是否有足够的磁盘空间

**Q: 生成的文件无法安装**
A: 检查源目录是否包含有效的VSCode插件结构

### 技术细节

- 使用.NET的`System.IO.Compression.FileSystem`库进行压缩
- 压缩级别设置为`Optimal`以获得最佳压缩比
- 支持Unicode文件名和路径
- 自动处理临时文件清理

## 版本历史

- v1.0: 初始版本，基本打包功能
- v1.1: 添加错误处理和文件验证
- v1.2: 修复编码问题，改进用户体验

---

**作者**: AI Assistant  
**最后更新**: 2025-08-05
